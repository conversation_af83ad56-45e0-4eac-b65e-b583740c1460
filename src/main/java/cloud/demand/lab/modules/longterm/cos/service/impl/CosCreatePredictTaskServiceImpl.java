package cloud.demand.lab.modules.longterm.cos.service.impl;

import cloud.demand.lab.common.exception.WrongWebParameterException;
import cloud.demand.lab.common.utils.LoginUtils;
import cloud.demand.lab.modules.longterm.cos.entity.CosLongtermPredictCategoryConfigDO;
import cloud.demand.lab.modules.longterm.cos.entity.CosLongtermPredictInputArgsDO;
import cloud.demand.lab.modules.longterm.cos.entity.CosLongtermPredictInputBigCustomerChangeDO;
import cloud.demand.lab.modules.longterm.cos.entity.CosLongtermPredictInputScaleDO;
import cloud.demand.lab.modules.longterm.cos.entity.CosLongtermPredictOutBigCustomerChangeDO;
import cloud.demand.lab.modules.longterm.cos.entity.CosLongtermPredictOutScaleByAlgorithmDO;
import cloud.demand.lab.modules.longterm.cos.entity.CosLongtermPredictTaskDO;
import cloud.demand.lab.modules.longterm.cos.enums.Constants;
import cloud.demand.lab.modules.longterm.cos.service.CosBigCustomerHistoryChangeService;
import cloud.demand.lab.modules.longterm.cos.service.CosCreatePredictTaskService;
import cloud.demand.lab.modules.longterm.cos.service.CosModelPredictService;
import cloud.demand.lab.modules.longterm.cos.utils.DateRangeUtils;
import cloud.demand.lab.modules.longterm.cos.web.dto.BigCustomerChangeDTO;
import cloud.demand.lab.modules.longterm.cos.web.dto.BigCustomerHistoryChangeDTO;
import cloud.demand.lab.modules.longterm.cos.web.dto.InputArgsDTO;
import cloud.demand.lab.modules.longterm.cos.web.req.CreatePredictTaskReq;
import cloud.demand.lab.modules.longterm.cos.web.req.QueryBigCustomerHistoryChangeReq;
import cloud.demand.lab.modules.longterm.cos.web.req.QueryCategoryForCreateReq;
import cloud.demand.lab.modules.longterm.cos.web.req.QueryPredictResultReq;
import cloud.demand.lab.modules.longterm.cos.web.resp.CreatePredictTaskResp;
import cloud.demand.lab.modules.longterm.cos.web.resp.QueryCategoryForCreateResp;
import cloud.demand.lab.modules.longterm.cos.web.resp.QueryPredictResultResp;
import cloud.demand.lab.modules.longterm.predict.enums.LongtermPredictTaskStatusEnum;
import cloud.demand.lab.modules.longterm.predict.enums.StrategyTypeEnum;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.io.IOUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.redis.RedisHelper;
import com.pugwoo.wooutils.redis.Synchronized;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import yunti.boot.exception.BizException;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class CosCreatePredictTaskServiceImpl implements CosCreatePredictTaskService {

    @Resource
    private DBHelper cdLabDbHelper;
    @Resource
    private RedisHelper redisHelper;
    @Resource
    private CosBigCustomerHistoryChangeService cosBigCustomerHistoryChangeService;
    @Resource
    private CosModelPredictService cosModelPredictService;

    @Override
    public QueryCategoryForCreateResp queryCategoryForCreate(QueryCategoryForCreateReq req) {
        List<CosLongtermPredictCategoryConfigDO> categoryConfigs = cdLabDbHelper.getAll(CosLongtermPredictCategoryConfigDO.class);

        QueryCategoryForCreateResp resp = new QueryCategoryForCreateResp();
        resp.setCategoryList(ListUtils.transform(categoryConfigs, o -> {
            QueryCategoryForCreateResp.Item item = new QueryCategoryForCreateResp.Item();
            item.setCategoryId(o.getId());
            item.setCategoryName(o.getCategory());
            LocalDate startDate = DateRangeUtils.getPredictStartDate(o);
            LocalDate endDate = DateRangeUtils.getPredictEndDate(o);
            item.setPredictStart(DateUtils.format(startDate, "yyyy-MM"));
            item.setPredictEnd(DateUtils.format(endDate, "yyyy-MM"));
            item.setInputArgDateRanges(DateRangeUtils.getDateRange(startDate, endDate, o.getIntervalMonth()));
            item.setStrategyTypes(ListUtils.transform(StrategyTypeEnum.values(), QueryCategoryForCreateResp.StrategyType::from));
            return item;
        }));
        return resp;
    }

    @SneakyThrows
    @Override
    @Synchronized(keyScript = "args[0].categoryId", customExceptionMessage = "当前方案正在创建任务中，暂不支持并发给同一方案创建预测任务，请稍后重试")
    @Transactional(value = "cdlabTransactionManager")
    public CreatePredictTaskResp createPredictTask(CreatePredictTaskReq req) {
        // 1. 参数校验
        if (req == null || req.getCategoryId() == null) {
            throw new WrongWebParameterException("方案id(categoryId)不能为空");
        }
        if (req.getInputArgs() == null || req.getInputArgs().isEmpty()) {
            throw new WrongWebParameterException("输入参数(inputArgs)不能为空");
        }

        // 2. 检查方案是否存在
        CosLongtermPredictCategoryConfigDO categoryConfig = getCategoryConfig(req.getCategoryId());
        // 3. 创建COS预测任务
        CosLongtermPredictTaskDO task = createTask(categoryConfig, req.getCategoryId(), req.getIsEnable());

        // 4. 如果预测月份的方案已经isEnable，则覆盖它
        if (req.getIsEnable() != null && req.getIsEnable()) {
            WhereSQL whereSQL = new WhereSQL();
            whereSQL.and("is_enable = ?", true);
            whereSQL.and("category_id = ?", task.getCategoryId());
            whereSQL.and("predict_start = ?", task.getPredictStart());

            cdLabDbHelper.updateAll(CosLongtermPredictTaskDO.class, "is_enable=false",
                    whereSQL.getSQL(), whereSQL.getParams());
            task.setIsEnable(true);
        }

        // 5. 创建任务
        cdLabDbHelper.insert(task);

        // 6. 保存输入参数
        saveInputArgs(req.getInputArgs(), task.getId());
        // 7. 保存大客户历史变化数据（冗余refTaskId对应任务的数据）
        saveBigCustomerHistoryChange(req.getRefTaskId(), req.getCategoryId(), task.getId());
        // 8. 保存存量数据和预测数据，即页面的图表数据
        saveScaleAndPredictData(req.getCategoryId(), task.getId(), req.getRefTaskId());
        // 9. 保存大客户未来预测数据
        saveBigCustomerForecast(req.getInputArgs(), task.getId());

        // 10. 发送创建任务消息，等待异步处理
        Runnable sendMsg = () -> redisHelper.send(Constants.REDIS_QUEUE_LONGTERM_PREDICT_TASK_COS,
                task.getId().toString(), 600);
        boolean submit = cdLabDbHelper.executeAfterCommit(sendMsg);
        if (!submit) {
            sendMsg.run();
        }

        CreatePredictTaskResp resp = new CreatePredictTaskResp();
        resp.setTaskId(task.getId());
        return resp;
    }

    @Override
    public CosLongtermPredictCategoryConfigDO getCategoryById(Long id) {
        if (id == null) {
            return null;
        }
        return cdLabDbHelper.getByKey(CosLongtermPredictCategoryConfigDO.class, id);
    }

    /**
     * 查找方案信息
     */
    private CosLongtermPredictCategoryConfigDO getCategoryConfig(Long categoryId) {
        if (categoryId == null) {
            throw new WrongWebParameterException("方案id(categoryId)不能为空");
        }
        CosLongtermPredictCategoryConfigDO categoryConfig = cdLabDbHelper.getByKey(CosLongtermPredictCategoryConfigDO.class, categoryId);
        if (categoryConfig == null) {
            throw new WrongWebParameterException("方案不存在");
        }
        return categoryConfig;
    }

    /**
     * 创建COS预测任务
     */
    private CosLongtermPredictTaskDO createTask(CosLongtermPredictCategoryConfigDO categoryConfig,
                                                Long categoryId, Boolean isEnable) {
        CosLongtermPredictTaskDO task = new CosLongtermPredictTaskDO();
        task.setCategoryId(categoryId);
        task.setCategoryName(categoryConfig.getCategory());
        task.setIsEnable(isEnable != null && isEnable);
        task.setParts(categoryConfig.getParts());
        task.setModelPart(categoryConfig.getModelPart());
        task.setIsSeparateInOut(categoryConfig.getIsSeparateInOut());
        task.setTaskStatus(LongtermPredictTaskStatusEnum.NEW.getCode());
        task.setCreator(LoginUtils.getUserNameWithSystem());
        task.setPredictStart(DateRangeUtils.getPredictStartDate(categoryConfig));
        task.setPredictEnd(DateRangeUtils.getPredictEndDate(categoryConfig));
        task.setConditionSql(categoryConfig.getWhereSql());

        try {
            String sql = IOUtils.readClasspathResourceAsString("/sql/longterm_predict/cos/plan_cos_scale.sql");
            sql = sql.replace("${CONDITION}", categoryConfig.getWhereSql());
            task.setInputSql(sql);
        } catch (Exception ignored) {}

        task.setPurchaseInputSql(""); // TODO 要读取sql文件，采购的
        task.setDimsName(categoryConfig.getDimsName());
        task.setScopeCustomer(categoryConfig.getScopeCustomer());
        task.setScopeResourcePool(categoryConfig.getScopeResourcePool());
        task.setIntervalMonth(categoryConfig.getIntervalMonth());
        return task;
    }

    /**
     * 保存输入参数
     */
    private void saveInputArgs(List<InputArgsDTO> inputArgs, Long taskId) {
        if (inputArgs == null || inputArgs.isEmpty()) {
            return;
        }

        List<CosLongtermPredictInputArgsDO> inputArgsDOList = ListUtils.transform(inputArgs, inputArg -> {
            CosLongtermPredictInputArgsDO inputArgsDO = new CosLongtermPredictInputArgsDO();
            inputArgsDO.setTaskId(taskId);
            inputArgsDO.setStrategyType(inputArg.getStrategyType());
            inputArgsDO.setDateName(inputArg.getDateName());
            inputArgsDO.setStartDate(DateUtils.parseLocalDate(inputArg.getStartDate()));
            inputArgsDO.setEndDate(DateUtils.parseLocalDate(inputArg.getEndDate()));
            inputArgsDO.setScaleGrowthRateIn(inputArg.getInnerScaleGrowthRate());
            inputArgsDO.setScaleGrowthRateOut(inputArg.getOuterScaleGrowthRate());
            inputArgsDO.setNote(inputArg.getNote());
            return inputArgsDO;
        });

        cdLabDbHelper.insertBatchWithoutReturnId(inputArgsDOList);
    }

    /**
     * 保存大客户历史变化数据（冗余refTaskId对应任务的数据）
     */
    private void saveBigCustomerHistoryChange(Long refTaskId, Long categoryId, Long newTaskId) {
        if (refTaskId == null) { // 如果没有refTaskId，则查询默认的大客户历史数据
            refTaskId = 0L;
        }

        // 查询大客户历史变化数据
        QueryBigCustomerHistoryChangeReq queryReq = new QueryBigCustomerHistoryChangeReq();
        queryReq.setCategoryId(categoryId);
        queryReq.setTaskId(refTaskId);

        List<BigCustomerHistoryChangeDTO> historyChangeList =
                cosBigCustomerHistoryChangeService.queryBigCustomerHistoryChange(queryReq).getDataList();

        if (historyChangeList != null && !historyChangeList.isEmpty()) {
            List<CosLongtermPredictInputBigCustomerChangeDO> bigCustomerChangeDOList =
                    ListUtils.transform(historyChangeList, historyChange -> {
                CosLongtermPredictInputBigCustomerChangeDO changeDO = historyChange.toDO();
                changeDO.setTaskId(newTaskId); // 设置为新任务的ID
                changeDO.setId(null); // 清空ID，让数据库自动生成
                return changeDO;
            });

            cdLabDbHelper.insertBatchWithoutReturnId(bigCustomerChangeDOList);
        }
    }

    /**
     * 保存存量数据
     */
    private void saveScaleAndPredictData(Long categoryId, Long taskId, Long refTaskId) {
        try {
            // 复用CosModelPredictController.queryPredictResult查询存量数据
            QueryPredictResultReq queryReq = new QueryPredictResultReq();
            queryReq.setCategoryId(categoryId);
            queryReq.setTaskId(refTaskId);

            QueryPredictResultResp predictResult = cosModelPredictService.queryPredictResult(queryReq);
            if (predictResult == null || predictResult.getLines() == null) {
                throw new BizException("保存预测输入数据异常，请稍后再试");
            }

            // 只需要保存内部和外部的数据，不用保存全部的
            predictResult.getLines().removeIf(line -> !line.getScope().contains("内部") && !line.getScope().contains("外部"));

            List<CosLongtermPredictInputScaleDO> scaleDataList = new ArrayList<>();
            List<CosLongtermPredictOutScaleByAlgorithmDO> predictDataList = new ArrayList<>();
            for (QueryPredictResultResp.Line line : predictResult.getLines()) {
                if (line.getPoints() != null && "HISTORY".equals(line.getType())) {
                    for (List<Object> point : line.getPoints()) {
                        if (point.size() >= 2) {
                            LocalDate date = (LocalDate) point.get(0);
                            BigDecimal value = (BigDecimal) point.get(1);

                            // 判断是否为外部客户数据
                            boolean isOutCustomer = line.getScope() != null && line.getScope().contains("外部");

                            // 保存剔除大客户后的存量数据
                            CosLongtermPredictInputScaleDO scaleDO = new CosLongtermPredictInputScaleDO();
                            scaleDO.setTaskId(taskId);
                            scaleDO.setDate(date);
                            scaleDO.setIsOutCustomer(isOutCustomer);
                            scaleDO.setCurScale(value);
                            scaleDataList.add(scaleDO);
                        }
                    }
                }

                // 保存预测结果
                if (line.getPoints() != null && "PREDICT".equals(line.getType())) {
                    for (List<Object> point : line.getPoints()) {
                        if (point.size() >= 2) {
                            LocalDate date = (LocalDate) point.get(0);
                            BigDecimal value = (BigDecimal) point.get(1);

                            // 判断是否为外部客户数据
                            boolean isOutCustomer = line.getScope() != null && line.getScope().contains("外部");

                            // 保存预测数据
                            CosLongtermPredictOutScaleByAlgorithmDO outScaleDO = new CosLongtermPredictOutScaleByAlgorithmDO();
                            outScaleDO.setTaskId(taskId);
                            outScaleDO.setDate(date);
                            outScaleDO.setIsOutCustomer(isOutCustomer);
                            outScaleDO.setPredictScale(value);
                            predictDataList.add(outScaleDO);
                        }
                    }
                }
            }

            cdLabDbHelper.insertBatchWithoutReturnId(scaleDataList);
            cdLabDbHelper.insertBatchWithoutReturnId(predictDataList);
        } catch (Exception e) {
            log.error("保存存量数据失败，taskId: {}, categoryId: {}", taskId, categoryId, e);
            // 不抛出异常，避免影响任务创建
        }
    }

    /**
     * 保存大客户未来预测数据
     */
    private void saveBigCustomerForecast(List<InputArgsDTO> inputArgs, Long taskId) {
        if (inputArgs == null || inputArgs.isEmpty()) {
            return;
        }

        List<CosLongtermPredictOutBigCustomerChangeDO> bigCustomerForecastList = new ArrayList<>();

        for (InputArgsDTO inputArg : inputArgs) {
            if (inputArg.getBigCustomerForecast() != null && !inputArg.getBigCustomerForecast().isEmpty()) {
                for (BigCustomerChangeDTO forecast : inputArg.getBigCustomerForecast()) {
                    CosLongtermPredictOutBigCustomerChangeDO forecastDO = new CosLongtermPredictOutBigCustomerChangeDO();
                    forecastDO.setTaskId(taskId);
                    forecastDO.setStrategyType(inputArg.getStrategyType());
                    forecastDO.setCustomerName(forecast.getCustomerName());
                    forecastDO.setIsOutCustomer(forecast.getIsOutCustomer());
                    forecastDO.setStartDate(forecast.getStartDate());
                    forecastDO.setEndDate(forecast.getEndDate());
                    forecastDO.setNetChange(forecast.getNetChange());
                    bigCustomerForecastList.add(forecastDO);
                }
            }
        }

        if (!bigCustomerForecastList.isEmpty()) {
            cdLabDbHelper.insertBatchWithoutReturnId(bigCustomerForecastList);
        }
    }

}
